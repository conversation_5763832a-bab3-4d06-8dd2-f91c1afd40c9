<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRC校验码学习 - 交互式动画教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transform: translateY(20px);
            opacity: 0;
            animation: slideUp 0.8s ease-out forwards;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .concept-box {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }

        .step-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }

        .step-card {
            flex: 1;
            min-width: 250px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .step-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: scale(0);
            transition: transform 0.6s ease;
        }

        .step-card:hover::before {
            transform: scale(1);
        }

        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            max-width: 100%;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
            transform: translateY(-2px);
        }

        .binary-display {
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            background: #2d3748;
            color: #68d391;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            letter-spacing: 2px;
        }

        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 4px;
            border-radius: 3px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-btn {
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: white;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .quiz-btn:hover {
            border-color: #667eea;
            background: #f7fafc;
            transform: translateY(-2px);
        }

        .quiz-btn.correct {
            background: #38a169;
            color: white;
            border-color: #38a169;
            animation: correctAnswer 0.6s ease;
        }

        .quiz-btn.incorrect {
            background: #e53e3e;
            color: white;
            border-color: #e53e3e;
            animation: shake 0.5s ease;
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .quiz-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            text-align: center;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .quiz-result.show {
            opacity: 1;
            transform: translateY(0);
        }

        .quiz-result.correct {
            background: #c6f6d5;
            color: #22543d;
            border: 2px solid #38a169;
        }

        .quiz-result.incorrect {
            background: #fed7d7;
            color: #742a2a;
            border: 2px solid #e53e3e;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .learning-section {
                padding: 20px;
            }

            .step-container {
                flex-direction: column;
            }

            .step-card {
                min-width: auto;
            }

            .quiz-options {
                grid-template-columns: 1fr;
            }
        }

        .step-detail {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            margin: 10px 0;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .step-detail:hover {
            border-color: #667eea;
            background: #edf2f7;
        }

        .step-detail.expanded {
            border-color: #38a169;
            background: #f0fff4;
        }

        .step-detail h4 {
            margin: 0;
            color: #4a5568;
            font-size: 1.1em;
        }

        .step-detail.expanded h4 {
            color: #38a169;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔢 CRC校验码学习</h1>
            <p>循环冗余校验 - 从零开始的交互式学习</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">� 就像小学除法一样简单！</h2>
            <div class="concept-box">
                <h3>🧮 还记得小学怎么做除法吗？</h3>
                <div style="display: flex; justify-content: space-around; align-items: center; margin: 30px 0; flex-wrap: wrap;">
                    <div style="text-align: center; margin: 20px;">
                        <div style="font-size: 2em;">👦</div>
                        <div style="font-family: monospace; font-size: 1.2em; background: #f0f8ff; padding: 15px; border-radius: 10px;">
                            &nbsp;&nbsp;13<br>
                            &nbsp;___<br>
                            4)52<br>
                            &nbsp;&nbsp;4<br>
                            &nbsp;&nbsp;--<br>
                            &nbsp;&nbsp;12<br>
                            &nbsp;&nbsp;12<br>
                            &nbsp;&nbsp;--<br>
                            &nbsp;&nbsp;&nbsp;0
                        </div>
                        <div><strong>普通除法</strong></div>
                    </div>

                    <div style="font-size: 3em; color: #667eea;">→</div>

                    <div style="text-align: center; margin: 20px;">
                        <div style="font-size: 2em;">🤖</div>
                        <div style="font-family: monospace; font-size: 1.2em; background: #f0fff0; padding: 15px; border-radius: 10px;">
                            &nbsp;&nbsp;&nbsp;11<br>
                            &nbsp;&nbsp;____<br>
                            101)1110<br>
                            &nbsp;&nbsp;&nbsp;101<br>
                            &nbsp;&nbsp;&nbsp;---<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;10
                        </div>
                        <div><strong>CRC除法</strong></div>
                    </div>
                </div>

                <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
                    <h3>� 关键区别：</h3>
                    <div style="font-size: 1.2em;">
                        普通除法用<strong>减法</strong> ➜ CRC除法用<strong>异或</strong><br>
                        但是<strong>位置规则完全一样！</strong>
                    </div>
                </div>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎮 一步一步学会竖式运算</h2>
            <div class="concept-box">
                <h3>🎯 我们要解决的题目</h3>
                <div class="binary-display">
                    信息码：111000110<br>
                    生成多项式：101011<br>
                    要计算：11100011000000 ÷ 101011
                </div>
            </div>

            <div class="step-container">
                <div class="step-card" onclick="showDivisionStep(1)">
                    <h3>🔍 第1步</h3>
                    <p>找到第一个可以除的位置</p>
                </div>
                <div class="step-card" onclick="showDivisionStep(2)">
                    <h3>📍 第2步</h3>
                    <p>对齐位置，准备运算</p>
                </div>
                <div class="step-card" onclick="showDivisionStep(3)">
                    <h3>⚡ 第3步</h3>
                    <p>做异或运算</p>
                </div>
                <div class="step-card" onclick="showDivisionStep(4)">
                    <h3>🔄 第4步</h3>
                    <p>继续下一位</p>
                </div>
            </div>

            <div id="divisionDemo" class="canvas-container">
                <div style="text-align: center; padding: 20px;">
                    <h3>👆 点击上面的步骤按钮，我来一步步教你！</h3>
                    <div style="font-size: 1.1em; color: #667eea; margin-top: 10px;">
                        每一步都会详细解释"为什么要这样做"
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <button class="btn btn-primary" onclick="playAllSteps()">🎬 自动播放全部步骤</button>
                <button class="btn btn-secondary" onclick="resetDemo()">🔄 重新开始</button>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">� 现在解决原题</h2>
            <div class="concept-box">
                <h3>📝 题目回顾</h3>
                <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin: 15px 0;">
                    <strong>题目：</strong>信息码字为111000110，生成多项式G(X)=x⁵+x³+x+1，计算CRC校验码
                </div>

                <h3>🔧 我们需要做什么？</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em;">📝</div>
                        <strong>步骤1</strong><br>
                        多项式转二进制<br>
                        101011
                    </div>
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em;">⬅️</div>
                        <strong>步骤2</strong><br>
                        信息码左移5位<br>
                        补5个0
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em;">➗</div>
                        <strong>步骤3</strong><br>
                        异或除法<br>
                        一步步计算
                    </div>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em;">🎉</div>
                        <strong>步骤4</strong><br>
                        得到校验码<br>
                        5位余数
                    </div>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>

            <div class="control-panel">
                <button class="btn btn-primary" onclick="startSimpleAnimation()">🚀 开始简单演示</button>
                <button class="btn btn-secondary" onclick="resetAnimation()">🔄 重新开始</button>
                <button class="btn btn-secondary" onclick="showStepByStep()">📚 分步详解</button>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🧮 计算过程</h2>
            <div id="calculationSteps"></div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎯 互动练习</h2>
            <div class="concept-box">
                <h3>🤔 现在轮到你了！</h3>
                <p>根据刚才学到的知识，选择正确的CRC校验码：</p>

                <div class="binary-display">
                    信息码：111000110<br>
                    生成多项式：G(X) = x⁵ + x³ + x + 1 → 101011<br>
                    CRC校验码是？
                </div>

                <div class="quiz-options">
                    <button class="quiz-btn" onclick="checkAnswer('01101')">A. 01101</button>
                    <button class="quiz-btn" onclick="checkAnswer('11001')">B. 11001</button>
                    <button class="quiz-btn" onclick="checkAnswer('001101')">C. 001101</button>
                    <button class="quiz-btn" onclick="checkAnswer('011001')">D. 011001</button>
                </div>

                <div id="quizResult" class="quiz-result"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎯 最终测验</h2>
            <div class="concept-box">
                <h3>🤔 现在你明白了吗？</h3>
                <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin: 15px 0;">
                    <strong>题目：</strong>信息码字为111000110，生成多项式G(X)=x⁵+x³+x+1，计算CRC校验码
                </div>

                <div class="binary-display">
                    经过我们一步步的学习，CRC校验码是：？
                </div>

                <div class="quiz-options">
                    <button class="quiz-btn" onclick="checkFinalAnswer('01101')">A. 01101</button>
                    <button class="quiz-btn" onclick="checkFinalAnswer('11001')">B. 11001</button>
                    <button class="quiz-btn" onclick="checkFinalAnswer('001101')">C. 001101</button>
                    <button class="quiz-btn" onclick="checkFinalAnswer('011001')">D. 011001</button>
                </div>

                <div id="finalResult" class="quiz-result"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📖 知识总结</h2>
            <div class="concept-box">
                <h3>🎓 你学会了什么？</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px;">
                        <h4>🧮 竖式除法</h4>
                        <p>就像小学除法一样，找到合适的位置开始计算</p>
                    </div>
                    <div style="background: #f3e5f5; padding: 20px; border-radius: 10px;">
                        <h4>⚡ 异或运算</h4>
                        <p>相同得0，不同得1，替代普通的减法运算</p>
                    </div>
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px;">
                        <h4>📍 位置对齐</h4>
                        <p>除数要对齐到被除数的正确位置才能计算</p>
                    </div>
                    <div style="background: #fff3e0; padding: 20px; border-radius: 10px;">
                        <h4>🔄 重复过程</h4>
                        <p>一步步重复，直到得到最终的余数</p>
                    </div>
                </div>

                <div class="binary-display" style="margin-top: 20px;">
                    🎉 恭喜！你已经掌握了CRC校验码的计算方法！<br>
                    记住：关键是理解"为什么要这样做"，而不是死记硬背
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let animationRunning = false;
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        // 题目数据
        const messageCode = '111000110';
        const generator = '101011';
        const dividend = messageCode + '00000'; // 左移5位
        
        function showDivisionStep(step) {
            const demoArea = document.getElementById('divisionDemo');
            currentStep = step;

            const steps = {
                1: {
                    title: "🔍 第1步：找到第一个可以除的位置",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px;">
                            <h4>🤔 问题：从哪里开始除？</h4>
                            <div style="font-family: monospace; font-size: 1.3em; line-height: 2; margin: 20px 0;">
                                被除数：<span style="background: #ffd700; padding: 2px;">1</span><span style="background: #ffd700; padding: 2px;">1</span><span style="background: #ffd700; padding: 2px;">1</span><span style="background: #ffd700; padding: 2px;">0</span><span style="background: #ffd700; padding: 2px;">0</span><span style="background: #ffd700; padding: 2px;">0</span>11000000<br>
                                除数：&nbsp;&nbsp;&nbsp;<span style="background: #ffcccb; padding: 2px;">1</span><span style="background: #ffcccb; padding: 2px;">0</span><span style="background: #ffcccb; padding: 2px;">1</span><span style="background: #ffcccb; padding: 2px;">0</span><span style="background: #ffcccb; padding: 2px;">1</span><span style="background: #ffcccb; padding: 2px;">1</span>
                            </div>

                            <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 20px 0;">
                                <strong>💡 规则：</strong><br>
                                1. 除数有6位数字<br>
                                2. 所以我们要从被除数的前6位开始看<br>
                                3. 前6位是：<strong>111000</strong><br>
                                4. 第一位是1，可以开始除法！
                            </div>

                            <div style="text-align: center; margin: 20px 0;">
                                <strong>就像小学除法：52÷4，我们先看5够不够除4一样！</strong>
                            </div>
                        </div>
                    `
                },
                2: {
                    title: "📍 第2步：对齐位置，准备运算",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px;">
                            <h4>📐 对齐很重要！</h4>
                            <div style="font-family: monospace; font-size: 1.3em; line-height: 2; margin: 20px 0; text-align: center;">
                                <div>被除数：11100011000000</div>
                                <div>除数：&nbsp;&nbsp;<span style="background: #ffcccb; padding: 2px;">101011</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;← 对齐到前6位</div>
                                <div style="margin-top: 20px;">
                                    <div>就像这样排列：</div>
                                    <div style="background: #fff; border: 2px solid #ccc; padding: 15px; margin: 10px 0;">
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;?<br>
                                        &nbsp;&nbsp;&nbsp;______<br>
                                        101011)11100011000000<br>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="background: #ffcccb; padding: 2px;">101011</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;← 写在这里<br>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;------
                                    </div>
                                </div>
                            </div>

                            <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 20px 0;">
                                <strong>💡 为什么要对齐？</strong><br>
                                就像小学除法，除数要对齐到被除数的相应位置，这样计算才正确！
                            </div>
                        </div>
                    `
                },
                3: {
                    title: "⚡ 第3步：做异或运算",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px;">
                            <h4>🧮 开始计算！</h4>
                            <div style="font-family: monospace; font-size: 1.3em; line-height: 2; margin: 20px 0;">
                                <div>111000 (被除数前6位)</div>
                                <div>101011 (除数)</div>
                                <div>------ (异或运算)</div>
                                <div style="background: #c6f6d5; padding: 5px;">010011 (结果)</div>
                            </div>

                            <div style="background: #fff3e0; padding: 15px; border-radius: 10px; margin: 20px 0;">
                                <strong>🔢 异或运算详解：</strong><br>
                                <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 10px; margin: 10px 0; text-align: center;">
                                    <div>1⊕1=<span style="color: red;">0</span></div>
                                    <div>1⊕0=<span style="color: red;">1</span></div>
                                    <div>1⊕1=<span style="color: red;">0</span></div>
                                    <div>0⊕0=<span style="color: red;">0</span></div>
                                    <div>0⊕1=<span style="color: red;">1</span></div>
                                    <div>0⊕1=<span style="color: red;">1</span></div>
                                </div>
                                <div style="text-align: center; font-weight: bold; color: red;">结果：010011</div>
                            </div>

                            <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 20px 0;">
                                <strong>💡 记住：</strong>异或就是"相同得0，不同得1"
                            </div>
                        </div>
                    `
                },
                4: {
                    title: "🔄 第4步：继续下一位",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px;">
                            <h4>🚶 继续往下走</h4>
                            <div style="font-family: monospace; font-size: 1.2em; line-height: 2; margin: 20px 0;">
                                <div>现在的情况：</div>
                                <div style="background: #fff; border: 2px solid #ccc; padding: 15px; margin: 10px 0;">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1<br>
                                    &nbsp;&nbsp;&nbsp;______<br>
                                    101011)11100011000000<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;101011<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;------<br>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="background: #c6f6d5; padding: 2px;">010011</span>1000000 ← 把后面的数字拉下来
                                </div>
                            </div>

                            <div style="background: #fff3e0; padding: 15px; border-radius: 10px; margin: 20px 0;">
                                <strong>🔄 下一步要做什么？</strong><br>
                                1. 看010011的第一位是0，不能除<br>
                                2. 向右移一位，看100111<br>
                                3. 第一位是1，可以除！<br>
                                4. 重复这个过程...
                            </div>

                            <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 20px 0; text-align: center;">
                                <strong>🎉 继续这样做，直到最后得到5位余数：11001</strong><br>
                                <div style="font-size: 1.2em; color: #e53e3e; margin-top: 10px;">
                                    这就是我们要的CRC校验码！
                                </div>
                            </div>
                        </div>
                    `
                }
            };

            if (steps[step]) {
                demoArea.innerHTML = `
                    <div class="concept-box">
                        <h3>${steps[step].title}</h3>
                        ${steps[step].content}
                    </div>
                `;
            }
        }

        function playAllSteps() {
            let currentStepIndex = 1;

            function showNextStep() {
                if (currentStepIndex <= 4) {
                    showDivisionStep(currentStepIndex);
                    currentStepIndex++;
                    setTimeout(showNextStep, 5000); // 每步停留5秒
                } else {
                    // 显示最终总结
                    document.getElementById('divisionDemo').innerHTML = `
                        <div class="concept-box">
                            <h3>🎉 完成！现在你明白了吗？</h3>
                            <div style="background: #c6f6d5; padding: 20px; border-radius: 10px; text-align: center;">
                                <div style="font-size: 1.5em; margin-bottom: 15px;">
                                    CRC校验码 = <strong>11001</strong>
                                </div>
                                <div style="font-size: 1.1em;">
                                    答案是 <strong>B. 11001</strong> ✅
                                </div>
                            </div>

                            <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <h4>🎓 总结一下：</h4>
                                <div style="text-align: left;">
                                    1. <strong>找位置：</strong>从被除数前几位开始（位数=除数位数）<br>
                                    2. <strong>对齐：</strong>除数对齐到相应位置<br>
                                    3. <strong>异或：</strong>相同得0，不同得1<br>
                                    4. <strong>继续：</strong>重复这个过程直到结束<br>
                                    5. <strong>余数：</strong>最后剩下的就是CRC校验码
                                </div>
                            </div>
                        </div>
                    `;
                }
            }

            showNextStep();
        }

        function resetDemo() {
            document.getElementById('divisionDemo').innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <h3>👆 点击上面的步骤按钮，我来一步步教你！</h3>
                    <div style="font-size: 1.1em; color: #667eea; margin-top: 10px;">
                        每一步都会详细解释"为什么要这样做"
                    </div>
                </div>
            `;
        }

        function showBasicConcept(step) {
            const learningArea = document.getElementById('basicLearningArea');
            currentStep = step;
            updateProgressBar();

            const concepts = {
                1: {
                    title: "🔢 认识二进制数字",
                    content: `
                        <div style="text-align: center; margin: 20px 0;">
                            <h3>二进制就像开关灯💡</h3>
                            <div style="display: flex; justify-content: center; gap: 20px; margin: 20px 0;">
                                <div style="text-align: center;">
                                    <div style="font-size: 3em;">💡</div>
                                    <div>灯亮 = 1</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 3em;">🔌</div>
                                    <div>灯灭 = 0</div>
                                </div>
                            </div>
                        </div>
                        <div class="binary-display">
                            我们题目中的数字：<br>
                            111000110 = 💡💡💡🔌🔌🔌💡💡🔌
                        </div>
                        <div style="margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 10px;">
                            <strong>练习：</strong>试着把下面的灯泡状态写成二进制<br>
                            💡🔌💡🔌💡 = <input type="text" id="binaryPractice1" style="padding: 5px; margin: 5px;" placeholder="输入答案">
                            <button onclick="checkBinaryAnswer('10101', 'binaryPractice1')" class="btn btn-secondary" style="padding: 5px 10px; margin: 5px;">检查</button>
                            <span id="binaryResult1"></span>
                        </div>
                    `
                },
                2: {
                    title: "➕ 学会异或运算（XOR）",
                    content: `
                        <div style="text-align: center; margin: 20px 0;">
                            <h3>异或运算就像"不同就是1"的游戏</h3>
                            <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <div style="font-size: 1.2em; margin: 10px 0;">
                                    <strong>规则很简单：</strong><br>
                                    相同 → 0 &nbsp;&nbsp;&nbsp; 不同 → 1
                                </div>
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0;">
                                    <div>
                                        <div>1 ⊕ 1 = 0 (相同)</div>
                                        <div>0 ⊕ 0 = 0 (相同)</div>
                                    </div>
                                    <div>
                                        <div>1 ⊕ 0 = 1 (不同)</div>
                                        <div>0 ⊕ 1 = 1 (不同)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="binary-display">
                            例子：101 ⊕ 110<br>
                            1⊕1=0, 0⊕1=1, 1⊕0=1<br>
                            结果：011
                        </div>
                        <div style="margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 10px;">
                            <strong>练习：</strong>计算 110 ⊕ 101<br>
                            答案：<input type="text" id="xorPractice1" style="padding: 5px; margin: 5px;" placeholder="输入答案">
                            <button onclick="checkXorAnswer('011', 'xorPractice1')" class="btn btn-secondary" style="padding: 5px 10px; margin: 5px;">检查</button>
                            <span id="xorResult1"></span>
                        </div>
                    `
                },
                3: {
                    title: "📝 理解多项式",
                    content: `
                        <div style="text-align: center; margin: 20px 0;">
                            <h3>多项式就像一个密码本📖</h3>
                            <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <div style="font-size: 1.1em;">
                                    G(X) = x⁵ + x³ + x + 1<br><br>
                                    这个式子告诉我们哪些位置有"1"：<br>
                                    第5位、第3位、第1位、第0位
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: center; margin: 20px 0;">
                            <table style="border-collapse: collapse; font-size: 1.1em;">
                                <tr style="background: #667eea; color: white;">
                                    <td style="padding: 10px; border: 1px solid #ccc;">位置</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">5</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">4</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">3</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">2</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">1</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">0</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ccc; background: #f7fafc;">数值</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; background: #c6f6d5;">1</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">0</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; background: #c6f6d5;">1</td>
                                    <td style="padding: 10px; border: 1px solid #ccc;">0</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; background: #c6f6d5;">1</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; background: #c6f6d5;">1</td>
                                </tr>
                            </table>
                        </div>
                        <div class="binary-display">
                            所以 G(X) = x⁵ + x³ + x + 1 = 101011
                        </div>
                    `
                },
                4: {
                    title: "🧮 简单除法练习",
                    content: `
                        <div style="text-align: center; margin: 20px 0;">
                            <h3>现在我们来做一个简单的除法</h3>
                            <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <div style="font-size: 1.1em;">
                                    就像小学学的长除法，但是用异或运算<br>
                                    我们要计算：1110 ÷ 101
                                </div>
                            </div>
                        </div>
                        <div style="font-family: monospace; font-size: 1.2em; background: #2d3748; color: #68d391; padding: 20px; border-radius: 10px; text-align: left;">
                            <div>    11   ← 商</div>
                            <div>   ___</div>
                            <div>101)1110  ← 被除数</div>
                            <div>    101   ← 第一步：1110的前3位111，不够除</div>
                            <div>    ---   　　　　　　取前4位1110</div>
                            <div>    101   ← 1110 ⊕ 1010 = 0100</div>
                            <div>    ---</div>
                            <div>     10   ← 余数</div>
                        </div>
                        <div style="margin: 20px 0; padding: 15px; background: #c6f6d5; border-radius: 10px;">
                            <strong>🎉 太棒了！</strong>现在你已经理解了基本概念！<br>
                            接下来我们就可以解决原题了！
                        </div>
                        <button onclick="showOriginalProblem()" class="btn btn-primary" style="margin: 10px;">现在解决原题 →</button>
                    `
                }
            };

            if (concepts[step]) {
                learningArea.innerHTML = `
                    <div class="concept-box">
                        <h3>${concepts[step].title}</h3>
                        ${concepts[step].content}
                    </div>
                `;
            }
        }

        function checkBinaryAnswer(correct, inputId) {
            const input = document.getElementById(inputId);
            const result = document.getElementById(inputId.replace('Practice', 'Result'));

            if (input.value.trim() === correct) {
                result.innerHTML = ' ✅ 正确！';
                result.style.color = '#38a169';
            } else {
                result.innerHTML = ` ❌ 答案是 ${correct}`;
                result.style.color = '#e53e3e';
            }
        }

        function checkXorAnswer(correct, inputId) {
            const input = document.getElementById(inputId);
            const result = document.getElementById(inputId.replace('Practice', 'Result'));

            if (input.value.trim() === correct) {
                result.innerHTML = ' ✅ 正确！';
                result.style.color = '#38a169';
            } else {
                result.innerHTML = ` ❌ 答案是 ${correct}`;
                result.style.color = '#e53e3e';
            }
        }

        function showOriginalProblem() {
            // 滚动到原题解析部分
            document.querySelector('.learning-section:nth-child(4)').scrollIntoView({
                behavior: 'smooth'
            });

            // 显示提示
            alert('🎯 很好！现在你已经掌握了基础知识，可以开始解决原题了！');
        }
        
        function updateProgressBar() {
            const progress = (currentStep / 4) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }
        
        function startSimpleAnimation() {
            if (animationRunning) return;
            animationRunning = true;
            currentStep = 0;
            showSimpleSteps();
        }

        function startAnimation() {
            if (animationRunning) return;
            animationRunning = true;
            currentStep = 0;
            animateCalculation();
        }

        function showStepByStep() {
            const stepsContainer = document.getElementById('calculationSteps');
            stepsContainer.innerHTML = `
                <div class="concept-box">
                    <h3>📚 超详细分步解析</h3>
                    <div id="detailedSteps">
                        <div class="step-detail" onclick="showDetailedStep(1)">
                            <h4>🔸 第1步：多项式转换 (点击展开)</h4>
                        </div>
                        <div class="step-detail" onclick="showDetailedStep(2)">
                            <h4>🔸 第2步：信息码处理 (点击展开)</h4>
                        </div>
                        <div class="step-detail" onclick="showDetailedStep(3)">
                            <h4>🔸 第3步：除法计算 (点击展开)</h4>
                        </div>
                        <div class="step-detail" onclick="showDetailedStep(4)">
                            <h4>🔸 第4步：得到答案 (点击展开)</h4>
                        </div>
                    </div>
                </div>
            `;
        }

        function showSimpleSteps() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const steps = [
                {
                    title: "第1步：多项式转换",
                    content: "G(X) = x⁵ + x³ + x + 1 → 101011",
                    detail: "有x的位置写1，没有x的位置写0"
                },
                {
                    title: "第2步：信息码左移",
                    content: "111000110 → 11100011000000",
                    detail: "在右边补5个0（因为多项式最高次是5）"
                },
                {
                    title: "第3步：异或除法",
                    content: "11100011000000 ÷ 101011",
                    detail: "用异或运算代替普通减法"
                },
                {
                    title: "第4步：得到校验码",
                    content: "余数：11001",
                    detail: "这就是我们要的CRC校验码！"
                }
            ];

            let stepIndex = 0;

            function drawSimpleStep() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                if (stepIndex < steps.length) {
                    const step = steps[stepIndex];

                    // 绘制标题
                    ctx.font = '24px Arial';
                    ctx.fillStyle = '#4a5568';
                    ctx.textAlign = 'center';
                    ctx.fillText(step.title, canvas.width/2, 50);

                    // 绘制主要内容
                    ctx.font = '20px Courier New';
                    ctx.fillStyle = '#2d3748';
                    ctx.fillText(step.content, canvas.width/2, 120);

                    // 绘制详细说明
                    ctx.font = '16px Arial';
                    ctx.fillStyle = '#667eea';
                    ctx.fillText(step.detail, canvas.width/2, 160);

                    // 绘制进度
                    ctx.fillStyle = '#38a169';
                    ctx.fillText(`进度: ${stepIndex + 1}/4`, canvas.width/2, 200);

                    // 如果是最后一步，显示答案
                    if (stepIndex === 3) {
                        ctx.font = '28px Arial';
                        ctx.fillStyle = '#e53e3e';
                        ctx.fillText('🎉 答案是 B: 11001', canvas.width/2, 280);
                    }

                    setTimeout(() => {
                        stepIndex++;
                        if (stepIndex <= steps.length) {
                            drawSimpleStep();
                        } else {
                            animationRunning = false;
                        }
                    }, 3000);
                }
            }

            drawSimpleStep();
        }
        
        function resetAnimation() {
            animationRunning = false;
            currentStep = 0;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateProgressBar();
            document.getElementById('calculationSteps').innerHTML = '';
        }
        
        function nextStep() {
            if (currentStep < 4) {
                currentStep++;
                updateProgressBar();
                showCalculationStep(currentStep);
            }
        }
        
        function animateCalculation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.font = '24px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.textAlign = 'center';
            ctx.fillText('CRC校验码计算过程', canvas.width/2, 30);
            
            // 绘制信息码和生成多项式
            ctx.font = '18px Courier New';
            ctx.fillStyle = '#2d3748';
            ctx.textAlign = 'left';
            ctx.fillText(`信息码: ${messageCode}`, 50, 80);
            ctx.fillText(`生成多项式: ${generator}`, 50, 110);
            ctx.fillText(`被除数: ${dividend}`, 50, 140);
            
            // 动画演示除法过程
            animateDivision();
        }
        
        function animateDivision() {
            // 详细的CRC计算动画
            let stepIndex = 0;
            const divisionSteps = performCRCCalculation();

            function drawStep() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = '20px Arial';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'center';
                ctx.fillText('CRC校验码计算 - 模2除法演示', canvas.width/2, 30);

                // 绘制当前步骤
                if (stepIndex < divisionSteps.length) {
                    const step = divisionSteps[stepIndex];
                    drawDivisionStep(step, stepIndex);

                    setTimeout(() => {
                        stepIndex++;
                        if (stepIndex <= divisionSteps.length) {
                            drawStep();
                        }
                    }, 2000);
                }
            }

            drawStep();
        }

        function performCRCCalculation() {
            let steps = [];
            let currentDividend = dividend;
            let position = 0;

            steps.push({
                dividend: currentDividend,
                divisor: generator,
                position: 0,
                operation: 'start',
                description: '开始计算：被除数 ÷ 除数'
            });

            while (currentDividend.length >= generator.length && position < currentDividend.length) {
                // 找到第一个1
                while (position < currentDividend.length && currentDividend[position] === '0') {
                    position++;
                }

                if (position + generator.length <= currentDividend.length) {
                    // 执行异或运算
                    let segment = currentDividend.substring(position, position + generator.length);
                    let result = '';

                    for (let i = 0; i < generator.length; i++) {
                        result += (parseInt(segment[i]) ^ parseInt(generator[i])).toString();
                    }

                    // 替换结果
                    currentDividend = currentDividend.substring(0, position) + result + currentDividend.substring(position + generator.length);

                    steps.push({
                        dividend: currentDividend,
                        divisor: generator,
                        position: position,
                        segment: segment,
                        result: result,
                        operation: 'xor',
                        description: `对 ${segment} 和 ${generator} 进行异或运算得到 ${result}`
                    });
                }

                position++;
                if (steps.length > 15) break; // 防止无限循环
            }

            // 最终结果
            const remainder = currentDividend.slice(-5).padStart(5, '0');
            steps.push({
                dividend: currentDividend,
                remainder: remainder,
                operation: 'result',
                description: `计算完成！CRC校验码为：${remainder}`
            });

            return steps;
        }

        function drawDivisionStep(step, index) {
            const startY = 70;
            const lineHeight = 25;

            ctx.font = '16px Courier New';
            ctx.fillStyle = '#2d3748';
            ctx.textAlign = 'left';

            // 绘制步骤说明
            ctx.fillStyle = '#667eea';
            ctx.font = '14px Arial';
            ctx.fillText(`步骤 ${index + 1}: ${step.description}`, 50, startY);

            // 绘制被除数
            ctx.fillStyle = '#2d3748';
            ctx.font = '18px Courier New';
            ctx.fillText('被除数: ', 50, startY + 30);

            // 高亮显示当前操作位置
            for (let i = 0; i < step.dividend.length; i++) {
                const x = 150 + i * 15;
                if (step.operation === 'xor' && i >= step.position && i < step.position + generator.length) {
                    ctx.fillStyle = '#ffd700';
                    ctx.fillRect(x - 2, startY + 15, 14, 20);
                }
                ctx.fillStyle = '#2d3748';
                ctx.fillText(step.dividend[i], x, startY + 30);
            }

            // 绘制除数
            if (step.operation === 'xor') {
                ctx.fillText('除数:   ', 50, startY + 60);
                for (let i = 0; i < generator.length; i++) {
                    const x = 150 + (step.position + i) * 15;
                    ctx.fillStyle = '#e53e3e';
                    ctx.fillText(generator[i], x, startY + 60);
                }

                // 绘制异或结果
                ctx.fillStyle = '#38a169';
                ctx.fillText('结果:   ', 50, startY + 90);
                for (let i = 0; i < step.result.length; i++) {
                    const x = 150 + (step.position + i) * 15;
                    ctx.fillText(step.result[i], x, startY + 90);
                }
            }

            // 绘制最终结果
            if (step.operation === 'result') {
                ctx.fillStyle = '#38a169';
                ctx.font = '20px Arial';
                ctx.fillText(`🎉 CRC校验码: ${step.remainder}`, 50, startY + 120);

                // 验证答案
                if (step.remainder === '11001') {
                    ctx.fillStyle = '#38a169';
                    ctx.fillText('✅ 正确答案是 B: 11001', 50, startY + 150);
                } else {
                    ctx.fillStyle = '#e53e3e';
                    ctx.fillText('❌ 计算有误，请检查', 50, startY + 150);
                }
            }
        }
        
        function showCalculationStep(step) {
            const stepsContainer = document.getElementById('calculationSteps');
            const stepExplanations = {
                1: {
                    title: '步骤1：理解生成多项式',
                    content: `
                        <div class="binary-display">
                            G(X) = x⁵ + x³ + x + 1<br>
                            对应二进制：1·x⁵ + 0·x⁴ + 1·x³ + 0·x² + 1·x¹ + 1·x⁰<br>
                            结果：<span class="highlight">101011</span>
                        </div>
                        <p>💡 多项式的每一项对应二进制的一位，有该项为1，无该项为0</p>
                    `
                },
                2: {
                    title: '步骤2：信息码左移',
                    content: `
                        <div class="binary-display">
                            原信息码：111000110<br>
                            左移5位（补5个0）：<span class="highlight">11100011000000</span>
                        </div>
                        <p>💡 左移的位数等于生成多项式的最高次数（5次）</p>
                    `
                },
                3: {
                    title: '步骤3：模2除法（异或运算）',
                    content: `
                        <div class="binary-display">
                            被除数：11100011000000<br>
                            除数：101011<br>
                            使用异或运算逐步计算...
                        </div>
                        <p>💡 模2除法就是异或运算，1⊕1=0, 1⊕0=1, 0⊕1=1, 0⊕0=0</p>
                    `
                },
                4: {
                    title: '步骤4：得到校验码',
                    content: `
                        <div class="binary-display">
                            最终余数：<span class="highlight">11001</span><br>
                            这就是我们要的CRC校验码！
                        </div>
                        <p>💡 答案是B：11001</p>
                    `
                }
            };
            
            if (stepExplanations[step]) {
                stepsContainer.innerHTML = `
                    <div class="concept-box">
                        <h3>${stepExplanations[step].title}</h3>
                        ${stepExplanations[step].content}
                    </div>
                `;
            }
        }
        
        function checkAnswer(selectedAnswer) {
            const correctAnswer = '11001';
            const quizButtons = document.querySelectorAll('.quiz-btn');
            const resultDiv = document.getElementById('quizResult');

            // 禁用所有按钮
            quizButtons.forEach(btn => {
                btn.disabled = true;
                btn.style.cursor = 'not-allowed';
            });

            // 找到被点击的按钮
            const clickedButton = Array.from(quizButtons).find(btn =>
                btn.textContent.includes(selectedAnswer)
            );

            if (selectedAnswer === correctAnswer) {
                // 正确答案
                clickedButton.classList.add('correct');
                resultDiv.innerHTML = `
                    <div style="font-size: 1.2em;">🎉 恭喜你答对了！</div>
                    <div style="margin-top: 10px;">
                        CRC校验码确实是 <strong>11001</strong><br>
                        你已经掌握了CRC计算的方法！
                    </div>
                `;
                resultDiv.className = 'quiz-result correct show';

                // 播放成功音效（如果需要）
                playSuccessAnimation();

            } else {
                // 错误答案
                clickedButton.classList.add('incorrect');

                // 高亮正确答案
                const correctButton = Array.from(quizButtons).find(btn =>
                    btn.textContent.includes(correctAnswer)
                );
                setTimeout(() => {
                    correctButton.classList.add('correct');
                }, 500);

                resultDiv.innerHTML = `
                    <div style="font-size: 1.2em;">❌ 答案不正确</div>
                    <div style="margin-top: 10px;">
                        正确答案是 <strong>B. 11001</strong><br>
                        建议重新观看动画演示来理解计算过程
                    </div>
                `;
                resultDiv.className = 'quiz-result incorrect show';
            }

            // 3秒后重置测验
            setTimeout(resetQuiz, 5000);
        }

        function resetQuiz() {
            const quizButtons = document.querySelectorAll('.quiz-btn');
            const resultDiv = document.getElementById('quizResult');

            quizButtons.forEach(btn => {
                btn.disabled = false;
                btn.style.cursor = 'pointer';
                btn.classList.remove('correct', 'incorrect');
            });

            resultDiv.classList.remove('show');
            setTimeout(() => {
                resultDiv.innerHTML = '';
                resultDiv.className = 'quiz-result';
            }, 500);
        }

        function playSuccessAnimation() {
            // 创建庆祝动画
            const canvas = document.getElementById('animationCanvas');
            const rect = canvas.getBoundingClientRect();

            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    createConfetti(rect);
                }, i * 100);
            }
        }

        function createConfetti(canvasRect) {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = (canvasRect.left + Math.random() * canvasRect.width) + 'px';
            confetti.style.top = canvasRect.top + 'px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = ['#667eea', '#764ba2', '#38a169', '#ffd700'][Math.floor(Math.random() * 4)];
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '1000';
            confetti.style.borderRadius = '50%';

            document.body.appendChild(confetti);

            // 动画
            let y = 0;
            let x = 0;
            const gravity = 0.5;
            const wind = (Math.random() - 0.5) * 2;
            let velocity = Math.random() * 5 + 5;

            const animate = () => {
                y += velocity;
                x += wind;
                velocity += gravity;

                confetti.style.transform = `translate(${x}px, ${y}px) rotate(${y * 2}deg)`;

                if (y < window.innerHeight) {
                    requestAnimationFrame(animate);
                } else {
                    document.body.removeChild(confetti);
                }
            };

            animate();
        }

        function checkFinalAnswer(selectedAnswer) {
            const correctAnswer = '11001';
            const quizButtons = document.querySelectorAll('.quiz-btn');
            const resultDiv = document.getElementById('finalResult');

            // 禁用所有按钮
            quizButtons.forEach(btn => {
                btn.disabled = true;
                btn.style.cursor = 'not-allowed';
            });

            // 找到被点击的按钮
            const clickedButton = Array.from(quizButtons).find(btn =>
                btn.textContent.includes(selectedAnswer)
            );

            if (selectedAnswer === correctAnswer) {
                // 正确答案
                clickedButton.classList.add('correct');
                resultDiv.innerHTML = `
                    <div style="font-size: 1.5em;">🎉 太棒了！你完全理解了！</div>
                    <div style="margin-top: 15px; font-size: 1.1em;">
                        <strong>B. 11001</strong> 就是正确答案！<br>
                        你已经掌握了CRC校验码的计算方法！
                    </div>
                    <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 10px;">
                        <strong>🎓 恭喜你学会了：</strong><br>
                        ✅ 理解竖式除法的位置规则<br>
                        ✅ 掌握异或运算的方法<br>
                        ✅ 知道为什么要这样计算<br>
                        ✅ 能够独立解决CRC问题
                    </div>
                `;
                resultDiv.className = 'quiz-result correct show';

                // 播放成功音效
                playSuccessAnimation();

            } else {
                // 错误答案
                clickedButton.classList.add('incorrect');

                // 高亮正确答案
                const correctButton = Array.from(quizButtons).find(btn =>
                    btn.textContent.includes(correctAnswer)
                );
                setTimeout(() => {
                    correctButton.classList.add('correct');
                }, 500);

                resultDiv.innerHTML = `
                    <div style="font-size: 1.2em;">😅 再想想看</div>
                    <div style="margin-top: 10px;">
                        正确答案是 <strong>B. 11001</strong><br>
                        建议重新看看上面的步骤演示
                    </div>
                    <div style="margin-top: 15px; padding: 15px; background: #fff3e0; border-radius: 10px;">
                        <strong>💡 提示：</strong><br>
                        回到上面点击"自动播放全部步骤"，<br>
                        仔细看每一步的计算过程
                    </div>
                `;
                resultDiv.className = 'quiz-result incorrect show';
            }

            // 5秒后重置
            setTimeout(() => {
                quizButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.style.cursor = 'pointer';
                    btn.classList.remove('correct', 'incorrect');
                });

                resultDiv.classList.remove('show');
                setTimeout(() => {
                    resultDiv.innerHTML = '';
                    resultDiv.className = 'quiz-result';
                }, 500);
            }, 8000);
        }

        function showDetailedStep(step) {
            const detailedSteps = {
                1: {
                    title: "🔸 第1步：多项式转换详解",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4>🎯 目标：把 G(X) = x⁵ + x³ + x + 1 转换成二进制</h4>

                            <div style="margin: 20px 0;">
                                <strong>方法：</strong>看看哪些位置有x，就在那个位置写1
                            </div>

                            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                                <tr style="background: #667eea; color: white;">
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">位置</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">x⁵</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">x⁴</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">x³</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">x²</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">x¹</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">x⁰</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #f7fafc;">有没有？</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5;">有✓</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">没有✗</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5;">有✓</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center;">没有✗</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5;">有✓</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5;">有✓</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #f7fafc;">二进制</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5; font-weight: bold;">1</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; font-weight: bold;">0</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5; font-weight: bold;">1</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; font-weight: bold;">0</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5; font-weight: bold;">1</td>
                                    <td style="padding: 10px; border: 1px solid #ccc; text-align: center; background: #c6f6d5; font-weight: bold;">1</td>
                                </tr>
                            </table>

                            <div class="binary-display">
                                结果：G(X) = 101011
                            </div>
                        </div>
                    `
                },
                2: {
                    title: "🔸 第2步：信息码处理详解",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4>🎯 目标：把信息码 111000110 左移5位</h4>

                            <div style="margin: 20px 0;">
                                <strong>为什么要左移5位？</strong><br>
                                因为生成多项式的最高次数是5（x⁵），所以要移5位
                            </div>

                            <div style="font-family: monospace; font-size: 1.2em; line-height: 1.8;">
                                原信息码：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="background: #ffd700; padding: 2px;">111000110</span><br>
                                左移5位（补5个0）：<span style="background: #ffd700; padding: 2px;">111000110</span><span style="background: #ffcccb; padding: 2px;">00000</span><br>
                                结果：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="background: #c6f6d5; padding: 2px;">11100011000000</span>
                            </div>

                            <div style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 10px;">
                                <strong>💡 小贴士：</strong>左移就是在右边加0，就像把数字往左推一样！
                            </div>
                        </div>
                    `
                },
                3: {
                    title: "🔸 第3步：异或除法详解",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4>🎯 目标：用异或运算做除法</h4>

                            <div style="margin: 20px 0;">
                                <strong>被除数：</strong>11100011000000<br>
                                <strong>除数：</strong>101011
                            </div>

                            <div style="font-family: monospace; font-size: 1.1em; background: #2d3748; color: #68d391; padding: 15px; border-radius: 10px;">
                                <div>第1步：看前6位 111000，第一位是1，可以除</div>
                                <div>111000 ⊕ 101011 = 010011</div>
                                <div></div>
                                <div>第2步：下移一位，看 100111，第一位是1，可以除</div>
                                <div>100111 ⊕ 101011 = 001100</div>
                                <div></div>
                                <div>第3步：继续这个过程...</div>
                                <div>最终得到余数：11001</div>
                            </div>

                            <div style="margin: 20px 0; padding: 15px; background: #fff3e0; border-radius: 10px;">
                                <strong>🔑 关键：</strong>异或运算就是"不同为1，相同为0"
                            </div>
                        </div>
                    `
                },
                4: {
                    title: "🔸 第4步：得到答案详解",
                    content: `
                        <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 15px 0;">
                            <h4>🎯 最终结果</h4>

                            <div style="text-align: center; margin: 30px 0;">
                                <div style="font-size: 2em; color: #e53e3e; font-weight: bold;">
                                    CRC校验码 = 11001
                                </div>
                            </div>

                            <div style="background: #c6f6d5; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <h4>✅ 验证答案</h4>
                                <div>题目选项：</div>
                                <div>A. 01101 ❌</div>
                                <div>B. 11001 ✅ <strong>正确答案！</strong></div>
                                <div>C. 001101 ❌</div>
                                <div>D. 011001 ❌</div>
                            </div>

                            <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <h4>🎓 总结</h4>
                                <div>1. 多项式转二进制：101011</div>
                                <div>2. 信息码左移5位：11100011000000</div>
                                <div>3. 异或除法计算</div>
                                <div>4. 得到5位余数：11001</div>
                            </div>
                        </div>
                    `
                }
            };

            if (detailedSteps[step]) {
                const stepElement = document.querySelector(`#detailedSteps .step-detail:nth-child(${step})`);
                if (stepElement.classList.contains('expanded')) {
                    // 收起
                    stepElement.classList.remove('expanded');
                    stepElement.innerHTML = `<h4>🔸 ${detailedSteps[step].title.replace('详解', '')} (点击展开)</h4>`;
                } else {
                    // 展开
                    stepElement.classList.add('expanded');
                    stepElement.innerHTML = `
                        <h4>🔸 ${detailedSteps[step].title} (点击收起)</h4>
                        ${detailedSteps[step].content}
                    `;
                }
            }
        }

        // 初始化
        window.onload = function() {
            // 添加一些初始动画效果
            const sections = document.querySelectorAll('.learning-section');
            sections.forEach((section, index) => {
                section.style.animationDelay = (index * 0.2) + 's';
            });

            // 添加滚动动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            sections.forEach(section => {
                observer.observe(section);
            });
        };
    </script>
</body>
</html>
